<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>level_1.jpg</key>
            <dict>
                <key>frame</key>
                <string>{{1030,2},{512,768}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{512,768}}</string>
                <key>sourceSize</key>
                <string>{512,768}</string>
            </dict>
            <key>level_2.jpg</key>
            <dict>
                <key>frame</key>
                <string>{{772,772},{512,768}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{512,768}}</string>
                <key>sourceSize</key>
                <string>{512,768}</string>
            </dict>
            <key>level_3.jpg</key>
            <dict>
                <key>frame</key>
                <string>{{516,2},{512,768}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{512,768}}</string>
                <key>sourceSize</key>
                <string>{512,768}</string>
            </dict>
            <key>level_4.jpg</key>
            <dict>
                <key>frame</key>
                <string>{{2,772},{512,768}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{512,768}}</string>
                <key>sourceSize</key>
                <string>{512,768}</string>
            </dict>
            <key>level_5.jpg</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{512,768}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{512,768}}</string>
                <key>sourceSize</key>
                <string>{512,768}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>levelBg.png</string>
            <key>size</key>
            <string>{1544,1286}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:b5d93f1d22e5305655e26c85c7e0f989$</string>
            <key>textureFileName</key>
            <string>levelBg.png</string>
        </dict>
    </dict>
</plist>
