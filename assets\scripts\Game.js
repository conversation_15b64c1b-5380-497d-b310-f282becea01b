
cc.Class({
    extends: cc.Component,

    properties: {
        bg1: cc.Node,
        bg2: cc.Node,
        scrollSpeed: 2, // 背景滚动速度
    },

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        let manager = cc.director.getCollisionManager();
        manager.enabled = true;

        // 设置初始位置
        if (this.bg1 && this.bg2) {
            // 假设背景图片高度为 winSize.height
            this.bg2.y = cc.winSize.height;
        }
    },


    start () {

    },

    update (dt) {
        this.bgroll(dt);
    },

    bgroll(dt) {
        if (!this.bg1 || !this.bg2) return;

        // 向下滚动
        this.bg1.y -= this.scrollSpeed;
        this.bg2.y -= this.scrollSpeed;

        // 当背景1完全移出屏幕底部，将其移到背景2的上方
        if (this.bg1.y <= -cc.winSize.height) {
            this.bg1.y = this.bg2.y + cc.winSize.height;
        }

        // 当背景2完全移出屏幕底部，将其移到背景1的上方
        if (this.bg2.y <= -cc.winSize.height) {
            this.bg2.y = this.bg1.y + cc.winSize.height;
        }
    },
});
