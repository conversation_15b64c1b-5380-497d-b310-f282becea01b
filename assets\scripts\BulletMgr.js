cc.Class({
    extends: cc.Component,

    properties: {
        bulletPrefab:cc.Prefab,
        player:cc.Node,
        
    },

    onLoad () {
        // this.schedule(this.creatBullet,0.1);
        // this.schedule(this.enemyCreatBullet,1);
    },

    start () {

    },

    update (dt) {},

    creatBullet(pos, owner){
        let bulletNode = cc.instantiate(this.bulletPrefab);
        bulletNode.parent = this.node;
        bulletNode.x = pos.x;
        bulletNode.y = pos.y;
        let bulletJs = bulletNode.getComponent("Bullet");
        bulletJs.init(owner); // 默认 owner 为 player
        bulletJs.move(); // 玩家子弹向上
    },

    enemyCreatBullet(pos, owner){
        let bulletNode = cc.instantiate(this.bulletPrefab);
        bulletNode.parent = this.node;
        bulletNode.x = pos.x;
        bulletNode.y = pos.y - 100;
        let bulletJs = bulletNode.getComponent("Bullet");
        bulletJs.init(owner); // 默认 owner 为 enemy
        if(bulletJs.enemyBulletMove){
            bulletJs.enemyBulletMove(); // 敌人子弹向下
        }
    },


    
});
