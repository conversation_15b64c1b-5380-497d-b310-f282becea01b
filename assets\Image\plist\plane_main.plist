<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>plane_main01.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{135,90}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{135,90}}</string>
                <key>sourceSize</key>
                <string>{135,90}</string>
            </dict>
            <key>plane_main02.png</key>
            <dict>
                <key>frame</key>
                <string>{{139,2},{131,105}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{131,105}}</string>
                <key>sourceSize</key>
                <string>{131,105}</string>
            </dict>
            <key>plane_main03.png</key>
            <dict>
                <key>frame</key>
                <string>{{272,2},{123,104}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{123,104}}</string>
                <key>sourceSize</key>
                <string>{123,104}</string>
            </dict>
            <key>plane_main04.png</key>
            <dict>
                <key>frame</key>
                <string>{{272,108},{120,100}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{120,100}}</string>
                <key>sourceSize</key>
                <string>{120,100}</string>
            </dict>
            <key>plane_main05.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,94},{126,80}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{126,80}}</string>
                <key>sourceSize</key>
                <string>{126,80}</string>
            </dict>
            <key>plane_main06.png</key>
            <dict>
                <key>frame</key>
                <string>{{394,179},{116,95}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{116,95}}</string>
                <key>sourceSize</key>
                <string>{116,95}</string>
            </dict>
            <key>plane_main07.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,251},{114,78}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{114,78}}</string>
                <key>sourceSize</key>
                <string>{114,78}</string>
            </dict>
            <key>plane_main08.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,176},{117,73}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{117,73}}</string>
                <key>sourceSize</key>
                <string>{117,73}</string>
            </dict>
            <key>plane_main09.png</key>
            <dict>
                <key>frame</key>
                <string>{{130,109},{117,94}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{117,94}}</string>
                <key>sourceSize</key>
                <string>{117,94}</string>
            </dict>
            <key>plane_main10.png</key>
            <dict>
                <key>frame</key>
                <string>{{121,205},{114,93}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{114,93}}</string>
                <key>sourceSize</key>
                <string>{114,93}</string>
            </dict>
            <key>plane_main11.png</key>
            <dict>
                <key>frame</key>
                <string>{{397,2},{110,89}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{110,89}}</string>
                <key>sourceSize</key>
                <string>{110,89}</string>
            </dict>
            <key>plane_main12.png</key>
            <dict>
                <key>frame</key>
                <string>{{348,276},{107,80}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{107,80}}</string>
                <key>sourceSize</key>
                <string>{107,80}</string>
            </dict>
            <key>plane_main13.png</key>
            <dict>
                <key>frame</key>
                <string>{{237,210},{109,82}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{109,82}}</string>
                <key>sourceSize</key>
                <string>{109,82}</string>
            </dict>
            <key>plane_main14.png</key>
            <dict>
                <key>frame</key>
                <string>{{397,93},{110,84}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{110,84}}</string>
                <key>sourceSize</key>
                <string>{110,84}</string>
            </dict>
            <key>plane_main15.png</key>
            <dict>
                <key>frame</key>
                <string>{{237,294},{103,77}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{103,77}}</string>
                <key>sourceSize</key>
                <string>{103,77}</string>
            </dict>
            <key>plane_main16.png</key>
            <dict>
                <key>frame</key>
                <string>{{118,300},{95,89}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{95,89}}</string>
                <key>sourceSize</key>
                <string>{95,89}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>plane_main.png</string>
            <key>size</key>
            <string>{512,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:dad99bcc03beb587339e61aded1360e7$</string>
            <key>textureFileName</key>
            <string>plane_main.png</string>
        </dict>
    </dict>
</plist>
