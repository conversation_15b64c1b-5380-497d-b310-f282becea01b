
cc.Class({
    extends: cc.Component,

    properties: {
     bulletMgrNode: cc.Node,
     playerMove:0,
     playerhp:5,
     playerhpBar:cc.ProgressBar,
    },

    // LIFE-CYCLE CALLBACKS:

     onLoad () {

                // 使用枚举类型来注册
        this.node.on(cc.Node.EventType.TOUCH_MOVE, function (event) {
 
        let woldPos = event.getLocation();
        // this.node.setPosition(woldPos.x, woldPos.y);
        let clickpos = this.node.parent.convertToNodeSpaceAR(woldPos);
        this.node.x= clickpos.x;
        this.node.y = clickpos.y;
 
        }, this);
        this.schedule(this.fireBullet,0.2);

        this.node.zIndex = 1; // 设置节点的zIndex，确保玩家在最上层

     },

    start () {

    },

    // update (dt) {},

    fireBullet(){
        let bulletMgr = cc.find("Canvas/BulletMgr").getComponent("BulletMgr");
        let pos = this.node.getPosition();
        bulletMgr.creatBullet(pos,"player"); // 玩家子弹向上
    },

    hurt(){
        this.playerhp -= 1;
        // 更新血条进度，使用 playerhp 属性，初始值为5
        this.playerhpBar.progress = this.playerhp / 5;
        if(this.playerhp <= 0){
            // let gameOverNode = cc.find("Canvas/GameOver");
            // gameOverNode.active = true; // 显示游戏结束界面
            // this.node.removeFromParent(); // 玩家死亡后移除玩家节点
        }
    },
});
