
cc.Class({
    extends: cc.Component,
   
    properties: {
        imgs:[cc.SpriteFrame],
        hpBar:cc.ProgressBar,

    },

    // LIFE-CYCLE CALLBACKS:

    onLoad () {

         this.schedule(this.enemyfireBullet,0.5);
    },

    // start () {

    // },

    // update (dt) {

      
    // },

    initWithData(enemyDt){
        this.hp = enemyDt.hp;
        this.maxHp = enemyDt.hp;


        this.score = enemyDt.score; // 添加分数属性，默认为0


        let imgName =enemyDt.img;
        for(let img of this.imgs){
            if(img.name === imgName){
                let sprite=this.node.getComponent(cc.Sprite);
            sprite.spriteFrame = img;
        }
    }

},
    move(){
        cc.tween(this.node)
        .by(5,{position:cc.v2(0,-cc.winSize.height)})
        .removeSelf()
        .start()
    },
hurt(){
    this.hp -= 1;

    this.hpBar.progress = this.hp / this.maxHp;
    if(this.hp <= 0){


        let scorenode = cc.find("Canvas/score");
        let scorejs =scorenode.getComponent("Score");
        scorejs.addScore(this.score); // 增加分数
        this.node.removeFromParent();
    }
},
 enemyfireBullet(){
        let bulletMgr = cc.find("Canvas/BulletMgr").getComponent("BulletMgr");
        let pos = this.node.getPosition();   
        bulletMgr.enemyCreatBullet(pos, "enemy"); // 敌人子弹向下
    }

});
