cc.Class({
    extends: cc.Component,

    properties: {

    },

    start () {

    },

    // update (dt) {},

    init(owner){
        this.owner = owner; // 记录子弹的拥有者（玩家或敌人
    },
 move(){
        cc.tween(this.node)
        .by(1,{position:cc.v2(0,cc.winSize.height)}) // 玩家子弹向上
        .removeSelf()
        .start()
    },
    // 敌人子弹向下
    enemyBulletMove(){
        this.node.angle = 180; // 设置敌人子弹向下
        cc.tween(this.node)
        .by(1.5,{position:cc.v2(0,-cc.winSize.height)})
        .removeSelf()
        .start()
    },
onCollisionEnter: function(other, self) {
   

    if(this.owner === other.node.group){
        // self.node.removeFromParent();
        return;
    }

    if(other.node.group ==="player"){
            // 敌人子弹打到玩家
       let playerJs = other.node.getComponent("player");
        playerJs.hurt();
        self.node.removeFromParent();
    }

        
    else if(other.node.group === "enemy"){
        // 只有玩家的子弹打到敌人才掉血
        if(this.owner === "player"){
            let enemyJs = other.node.getComponent("Enemy");
            enemyJs.hurt();
            self.node.removeFromParent();
        }

        // 子弹打子弹
    }

    else if(other.node.group === "bullet"){

        
        
        let selfBulletJs = self.node.getComponent("Bullet");
        let otherBulletJs = other.node.getComponent("Bullet");
        if(selfBulletJs.owner !== otherBulletJs.owner){    
            other.node.removeFromParent();
            self.node.removeFromParent();
        }
    

    }

    }
});
