<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>enemy01.png</key>
            <dict>
                <key>frame</key>
                <string>{{457,2},{256,18}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{256,18}}</string>
                <key>sourceSize</key>
                <string>{256,18}</string>
            </dict>
            <key>enemy02.png</key>
            <dict>
                <key>frame</key>
                <string>{{470,374},{251,9}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{251,9}}</string>
                <key>sourceSize</key>
                <string>{251,9}</string>
            </dict>
            <key>enemy03.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{259,196}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{259,196}}</string>
                <key>sourceSize</key>
                <string>{259,196}</string>
            </dict>
            <key>enemy04.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,200},{246,201}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{246,201}}</string>
                <key>sourceSize</key>
                <string>{246,201}</string>
            </dict>
            <key>enemy05.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,535},{135,98}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{135,98}}</string>
                <key>sourceSize</key>
                <string>{135,98}</string>
            </dict>
            <key>enemy06.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,403},{186,130}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{186,130}}</string>
                <key>sourceSize</key>
                <string>{186,130}</string>
            </dict>
            <key>enemy07.png</key>
            <dict>
                <key>frame</key>
                <string>{{263,2},{192,133}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{192,133}}</string>
                <key>sourceSize</key>
                <string>{192,133}</string>
            </dict>
            <key>enemy08.png</key>
            <dict>
                <key>frame</key>
                <string>{{250,272},{158,118}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{158,118}}</string>
                <key>sourceSize</key>
                <string>{158,118}</string>
            </dict>
            <key>enemy09.png</key>
            <dict>
                <key>frame</key>
                <string>{{410,272},{100,73}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{100,73}}</string>
                <key>sourceSize</key>
                <string>{100,73}</string>
            </dict>
            <key>enemy10.png</key>
            <dict>
                <key>frame</key>
                <string>{{262,475},{103,74}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{103,74}}</string>
                <key>sourceSize</key>
                <string>{103,74}</string>
            </dict>
            <key>enemy11.png</key>
            <dict>
                <key>frame</key>
                <string>{{263,137},{175,133}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{175,133}}</string>
                <key>sourceSize</key>
                <string>{175,133}</string>
            </dict>
            <key>enemy12.png</key>
            <dict>
                <key>frame</key>
                <string>{{241,551},{97,75}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{97,75}}</string>
                <key>sourceSize</key>
                <string>{97,75}</string>
            </dict>
            <key>enemy13.png</key>
            <dict>
                <key>frame</key>
                <string>{{262,392},{113,81}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{113,81}}</string>
                <key>sourceSize</key>
                <string>{113,81}</string>
            </dict>
            <key>enemy14.png</key>
            <dict>
                <key>frame</key>
                <string>{{377,392},{91,81}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{91,81}}</string>
                <key>sourceSize</key>
                <string>{91,81}</string>
            </dict>
            <key>enemy15.png</key>
            <dict>
                <key>frame</key>
                <string>{{367,475},{99,70}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{99,70}}</string>
                <key>sourceSize</key>
                <string>{99,70}</string>
            </dict>
            <key>enemy16.png</key>
            <dict>
                <key>frame</key>
                <string>{{340,551},{70,62}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{70,62}}</string>
                <key>sourceSize</key>
                <string>{70,62}</string>
            </dict>
            <key>enemy17.png</key>
            <dict>
                <key>frame</key>
                <string>{{139,547},{100,76}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{100,76}}</string>
                <key>sourceSize</key>
                <string>{100,76}</string>
            </dict>
            <key>enemy18.png</key>
            <dict>
                <key>frame</key>
                <string>{{190,403},{70,142}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{70,142}}</string>
                <key>sourceSize</key>
                <string>{70,142}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>enemy.png</string>
            <key>size</key>
            <string>{485,635}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:906a5b112fba1a8060487117d3364a51$</string>
            <key>textureFileName</key>
            <string>enemy.png</string>
        </dict>
    </dict>
</plist>
